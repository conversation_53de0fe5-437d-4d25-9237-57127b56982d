"""
URL configuration for nextgen project.
"""
from django.contrib import admin
from django.urls import path, include, re_path
from django.conf import settings
from django.conf.urls.static import static
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from django.views.static import serve


schema_view = get_schema_view(
    openapi.Info(
        title="NextGen API",
        default_version='v1',
        description="API documentation for NextGen Backend",
        terms_of_service="https://www.google.com/policies/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="BSD License"),
    ),
    public=True,
    permission_classes=(permissions.AllowAny,),
)

# Define specific URL patterns BEFORE the admin catch-all
urlpatterns = [
    path('api/', include('apps.urls')),
    path('ckeditor5/', include('django_ckeditor_5.urls')),
    
    # Swagger URLs for API Documentation
    path('swagger<format>/', schema_view.without_ui(cache_timeout=0), name='schema-json'),
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
]

# CRITICAL: Add media URLs in DEBUG mode BEFORE admin
if settings.DEBUG:
    urlpatterns += [
        re_path(r'^media/(?P<path>.*)$', serve, name='media'),
    ]
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

# Admin at root - MUST be last because it catches everything else
urlpatterns += [
    path('', admin.site.urls),
]