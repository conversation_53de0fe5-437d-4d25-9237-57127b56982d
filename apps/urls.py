from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    OrganizationViewSet,
    PositionViewSet,
    TeamViewSet,
    AboutViewSet,
    AboutVideoViewSet,
    PopupViewSet,
    UniversitiesViewSet,
    DestinationViewSet,
    ServiceViewSet,
    TestPreparationViewSet,
    TestomonialViewSet,
    SendUsMessageViewSet,
    ConsultancyFormViewSet,
    HeroViewSet,
    ExperienceViewSet,
)

router = DefaultRouter()
router.register(r'organizations', OrganizationViewSet)
router.register(r'positions', PositionViewSet)
router.register(r'team', TeamViewSet)
router.register(r'about', AboutViewSet)
router.register(r'about-videos', AboutVideoViewSet)
router.register(r'popups', PopupViewSet)
router.register(r'universities', UniversitiesViewSet)
router.register(r'destinations', DestinationViewSet)
router.register(r'services', ServiceViewSet)
router.register(r'test-preparations', TestPreparationViewSet)
router.register(r'testimonials', TestomonialViewSet)
router.register(r'send-messages', SendUsMessageViewSet)
router.register(r'consultancy-forms', ConsultancyFormViewSet)
router.register(r'heroes', HeroViewSet)
router.register(r'experiences', ExperienceViewSet)

urlpatterns = [
    path('', include(router.urls)),
] 
