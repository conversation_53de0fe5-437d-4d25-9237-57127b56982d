from django.db import models
from django.utils.translation import gettext_lazy as _
from django_ckeditor_5.fields import CKE<PERSON>or5<PERSON><PERSON>
from django.core.validators import RegexValidator, MinValueValidator, MaxValueValidator
from django.core.exceptions import ValidationError
import os
from django_jsonform.models.fields import <PERSON><PERSON><PERSON><PERSON>


def validate_video_file(value):
    ext = os.path.splitext(value.name)[1]  # get file extension
    valid_extensions = [".mp4", ".mov", ".avi", ".webm", ".mkv"]
    if not ext.lower() in valid_extensions:
        raise ValidationError("Unsupported file extension.")


class Organization(models.Model):
    name = models.CharField(max_length=255)
    address = models.CharField(max_length=255)
    logo = models.ImageField(upload_to="organization/", blank=True, null=True)
    contact_number = models.CharField(
        max_length=10,
        validators=[
            RegexValidator(
                regex=r"^\d{10}$",
                message="Phone number must 10 digits",
            )
        ],
    )
    email = models.EmailField(max_length=255)
    pan_no = models.CharField(max_length=255, blank=True, null=True)
    reg_no = models.CharField(max_length=255, blank=True, null=True)
    description = models.CharField(max_length=255, blank=True, null=True)

    def __str__(self):
        return f"{self.name} {self.address}"

    class Meta:
        verbose_name = "Organization"
        verbose_name_plural = "Organizations"


class Position(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Position"
        verbose_name_plural = "Positions"


class Team(models.Model):
    id = models.AutoField(primary_key=True)
    first_name = models.CharField(max_length=255)
    middle_name = models.CharField(max_length=255, blank=True, null=True)
    last_name = models.CharField(max_length=255)
    image = models.ImageField(upload_to="team/", blank=True, null=True)
    position = models.ForeignKey(Position, on_delete=models.CASCADE)
    experience = models.CharField(max_length=255)
    uploaded_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.first_name} {self.last_name} {self.position} {self.experience}"

    class Meta:
        verbose_name = "Team"
        verbose_name_plural = "Teams"


class About(models.Model):
    story = CKEditor5Field()
    mission = JSONField(
        schema={"type": "array", "items": {"type": "string"}}, blank=True, null=True
    )
    vision = JSONField(
        schema={"type": "array", "items": {"type": "string"}}, blank=True, null=True
    )
    image = models.ImageField(upload_to="about_images/", blank=True, null=True)
    uploaded_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return "About Section"

    class Meta:
        verbose_name = "About"
        verbose_name_plural = "Abouts"


class AboutVideo(models.Model):
    about = models.ForeignKey(About, on_delete=models.CASCADE, related_name="videos")
    video = models.FileField(
        upload_to="about_videos/",
        validators=[validate_video_file],
        blank=True,
        null=True,
    )
    order = models.PositiveIntegerField(default=0)
    uploaded_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Video for {self.about} - Order {self.order}"

    class Meta:
        verbose_name = "About Video"
        verbose_name_plural = "About Videos"


class Popup(models.Model):
    id = models.AutoField(primary_key=True)
    image = models.ImageField(upload_to="popups/images/", blank=True, null=True)
    video = models.FileField(
        upload_to="popups/videos/",
        validators=[validate_video_file],
        blank=True,
        null=True,
    )
    uploaded_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Popup"
        verbose_name_plural = "Popups"


class Universities(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    image = models.ImageField(upload_to="universities/", blank=True, null=True)
    ranking = models.CharField(max_length=255)
    about = models.TextField(null=True, blank=True)
    program_offered = JSONField(
        schema={"type": "array", "items": {"type": "string"}}, blank=True, null=True
    )
    admission_required = JSONField(
        schema={"type": "array", "items": {"type": "string"}}, blank=True, null=True
    )
    cost = JSONField(
        schema={
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "tuition_fee": {"type": "string"},
                    "cost": {"type": "string"},
                    "living_expense": {"type": "string"},
                    "expense": {"type": "string"},
                },
                "required": ["tuition_fee", "cost", "living_expense", "expense"],
            },
        },
        blank=True,
        null=True,
    )
    scholarship = JSONField(
        schema={"type": "array", "items": {"type": "string"}}, blank=True, null=True
    )

    def __str__(self):
        return f"{self.name}"

    class Meta:
        verbose_name = "University"
        verbose_name_plural = "Universities"
        db_table = "universities"


class Destination(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    image = models.ImageField(upload_to="destination/", blank=True, null=True)
    programs = models.CharField(max_length=255)
    uni = models.CharField(max_length=255)
    flag = models.CharField(max_length=10)
    description = models.TextField(blank=True, null=True)
    why_study = JSONField(
        schema={"type": "array", "items": {"type": "string"}}, blank=True, null=True
    )
    top_universities = JSONField(
        schema={"type": "array", "items": {"type": "string"}}, blank=True, null=True
    )
    popular_courses = JSONField(
        schema={"type": "array", "items": {"type": "string"}}, blank=True, null=True
    )
    cost_of_study = JSONField(
        schema={
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "tuition_fee": {"type": "string"},
                    "cost": {"type": "string"},
                    "living_expense": {"type": "string"},
                    "expense": {"type": "string"},
                    "total_expense": {"type": "string"},
                    "total_cost": {"type": "string"},
                },
                "required": [
                    "tuition_fee",
                    "cost",
                    "living_expense",
                    "expense",
                    "total_expense",
                    "total_cost",
                ],
            },
        },
        blank=True,
        null=True,
    )

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Destination"
        verbose_name_plural = "Destinations"
        db_table = "destinations"


class Service(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    svg = models.CharField(max_length=10000, blank=True, null=True)
    description = models.TextField()
    why_to_choose = JSONField(
        schema={"type": "array", "items": {"type": "string"}}, blank=True, null=True
    )
    offer = JSONField(
        schema={
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "service": {"type": "string"},
                    "summary": {"type": "string"},
                },
                "required": ["service", "summary"],
            },
        },
        blank=True,
        null=True,
    )
    process = JSONField(
        schema={
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "process": {"type": "string"},
                    "summary": {"type": "string"},
                },
                "required": ["process", "summary"],
            },
        },
        blank=True,
        null=True,
    )

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Service"
        verbose_name_plural = "Services"
        db_table = "services"


class TestPreparation(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    description = models.TextField()
    about = models.TextField()
    modules = JSONField(
        schema={
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "name": {"type": "string"},
                    "summary": {"type": "string"},
                    "duration": {"type": "integer", "minimum": 1, "maximum": 365},
                    "section": {"type": "integer", "minimum": 1, "maximum": 10},
                },
                "required": ["name", "summary", "duration", "section"],
            },
        },
        blank=True,
        null=True,
    )
    process = JSONField(
        schema={
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "name": {"type": "string"},
                    "summary": {"type": "string"},
                },
                "required": ["name", "summary"],
            },
        },
        blank=True,
        null=True,
    )
    features = JSONField(
        schema={
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "name": {"type": "string"},
                    "summary": {"type": "string"},
                },
                "required": ["name", "summary"],
            },
        },
        blank=True,
        null=True,
    )

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Test Preparation"
        verbose_name_plural = "Test Preparations"


class Testomonial(models.Model):
    rating_choice = (
        ("one", "1"),
        ("two", "2"),
        ("three", "3"),
        ("four", "4"),
        ("five", "5"),
    )
    name = models.CharField(max_length=255)
    img = models.ImageField(upload_to="testomonial/", blank=True, null=True)
    uni = models.CharField(max_length=255)
    location = models.CharField(max_length=255)
    rating = models.CharField(choices=rating_choice, max_length=10)
    message = models.TextField()

    def __str__(self):
        return f"{self.name} {self.message} {self.rating}"

    class Meta:
        verbose_name = "Testomonial"
        verbose_name_plural = "Testomonials"


class ConsultancyForm(models.Model):
    education_level = (
        ("12", "12 Grade"),
        ("Diploma", "Diploma"),
        ("Bachelor", "Bachelor"),
        ("Masters", "Masters"),
        ("Phd", "Phd"),
    )

    full_name = models.CharField(max_length=355)
    contact_number = models.CharField(
        max_length=10,
        validators=[
            RegexValidator(
                regex=r"^\d{10}$",
                message="Phone number must 10 digits",
            )
        ],
    )
    email = models.EmailField(max_length=255)
    education = models.CharField(choices=education_level, max_length=20)
    country = models.ForeignKey(Destination, on_delete=models.CASCADE)
    test = models.ForeignKey(TestPreparation, on_delete=models.CASCADE)
    message = models.TextField()

    def __str__(self):
        return f"{self.full_name}{self.contact_number}{self.email}{self.education}"

    class Meta:
        verbose_name = "Consultancy Form"
        verbose_name_plural = "Consultancy Forms"


class SendUsMessage(models.Model):
    full_name = models.CharField(max_length=355)
    email = models.EmailField(max_length=255)
    contact_number = models.CharField(
        max_length=10,
        validators=[
            RegexValidator(
                regex=r"^\d{10}$",
                message="Phone number must be 10 digits",
            )
        ],
    )
    service = models.ForeignKey(Service, on_delete=models.CASCADE)
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.full_name} - {self.service.name}"

    class Meta:
        verbose_name = "Send Us Message"
        verbose_name_plural = "Send Us Messages"


class Hero(models.Model):
    id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=255)
    description = models.CharField(max_length=1000)
    image = models.ImageField(upload_to="hero/", blank=True, null=True)
    video = models.FileField(
        upload_to="hero/videos/",
        validators=[validate_video_file],
        blank=True,
        null=True,
    )
    uploaded_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Hero"
        verbose_name_plural = "Heroes"


class Experience(models.Model):
    id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=255)
    about = models.TextField(blank=True, null=True)
    experience = JSONField(
        schema={"type": "array", "items": {"type": "string"}}, blank=True, null=True
    )
    image = models.ImageField(upload_to="experience/", blank=True, null=True)
    uploaded_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Experience"
        verbose_name_plural = "Experiences"
