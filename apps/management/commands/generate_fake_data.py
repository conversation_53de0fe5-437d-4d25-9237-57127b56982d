from django.core.management.base import BaseCommand
from faker import Faker
from apps.models import (
    BlogPost,
    Organization,
    Position,
    Team,
    About,
    Popup,
    Universities,
    Destination,
    Service,
    TestPreparation,
    Testomonial,
    ConsultancyForm,
    SendUsMessage,
)
import random
from django.core.files.base import ContentFile
from PIL import Image
import io

fake = Faker()


class Command(BaseCommand):
    help = "Generate fake data for testing"

    def handle(self, *args, **kwargs):
        try:
            self.stdout.write("Generating fake data...")

            # Generate Positions
            positions = []
            position_titles = ["CEO", "Manager", "Consultant", "Advisor", "Coordinator"]
            for title in position_titles:
                position = Position.objects.create(name=title)
                positions.append(position)
            self.stdout.write("✓ Generated positions")

            # Generate Organization
            Organization.objects.create(
                name=fake.company(),
                address=fake.address(),
                contact_number=fake.numerify("##########"),
                email=fake.email(),
                pan_no=fake.numerify("ABCDE####F"),
                reg_no=fake.numerify("REG#######"),
                description=fake.text(),
            )
            self.stdout.write("✓ Generated organization")

            # Generate Team members
            for _ in range(5):
                Team.objects.create(
                    first_name=fake.first_name(),
                    middle_name=fake.first_name(),
                    last_name=fake.last_name(),
                    position=random.choice(positions),
                    experience=f"{random.randint(1, 20)} years",
                )
            self.stdout.write("✓ Generated team members")

            # Generate About
            About.objects.create(
                story=fake.text(max_nb_chars=1000),
                mission=fake.text(max_nb_chars=500),
                vision=fake.text(max_nb_chars=500),
            )
            self.stdout.write("✓ Generated about section")

            # Generate Universities
            for _ in range(5):
                Universities.objects.create(
                    name=fake.company(),
                    ranking=f"#{random.randint(1, 100)}",
                    description=fake.text(max_nb_chars=1000),
                )
            self.stdout.write("✓ Generated universities")

            # Generate Destinations
            destinations = []
            countries = ["USA", "UK", "Canada", "Australia", "Germany"]
            for country in countries:
                destination = Destination.objects.create(
                    name=country,
                    programs=fake.text(max_nb_chars=200),
                    uni=fake.company(),
                    description=fake.text(max_nb_chars=1000),
                )
                destinations.append(destination)
            self.stdout.write("✓ Generated destinations")

            # Generate Services
            services = []
            service_names = [
                "Study Abroad",
                "Visa Assistance",
                "Test Preparation",
                "Career Counseling",
                "Documentation",
            ]
            for name in service_names:
                service = Service.objects.create(
                    name=name,
                    description=fake.text(max_nb_chars=200),
                    offer=fake.text(max_nb_chars=500),
                    process=fake.text(max_nb_chars=500),
                )
                services.append(service)
            self.stdout.write("✓ Generated services")

            # Generate Test Preparations
            test_preps = []
            test_names = ["IELTS", "TOEFL", "GRE", "GMAT", "SAT"]
            for name in test_names:
                test_prep = TestPreparation.objects.create(
                    name=name,
                    description=fake.text(max_nb_chars=200),
                    about=fake.text(max_nb_chars=500),
                    modules=fake.text(max_nb_chars=500),
                    process=fake.text(max_nb_chars=500),
                    features=fake.text(max_nb_chars=500),
                )
                test_preps.append(test_prep)
            self.stdout.write("✓ Generated test preparations")

            # Generate Testimonials
            ratings = ["one", "two", "three", "four", "five"]
            for _ in range(10):
                Testomonial.objects.create(
                    name=fake.name(),
                    uni=fake.company(),
                    location=fake.city(),
                    rating=random.choice(ratings),
                    message=fake.text(max_nb_chars=200),
                )
            self.stdout.write("✓ Generated testimonials")

            # Generate Consultancy Forms
            education_levels = ["12", "Diploma", "Bachelor", "Masters", "Phd"]
            for _ in range(20):
                ConsultancyForm.objects.create(
                    full_name=fake.name(),
                    contact_number=fake.numerify("##########"),
                    email=fake.email(),
                    education=random.choice(education_levels),
                    country=random.choice(destinations),
                    test=random.choice(test_preps),
                    message=fake.text(max_nb_chars=200),
                )
            self.stdout.write("✓ Generated consultancy forms")

            # Generate Send Us Messages
            for _ in range(15):
                SendUsMessage.objects.create(
                    full_name=fake.name(),
                    email=fake.email(),
                    contact_number=fake.numerify("##########"),
                    service=random.choice(services),
                    message=fake.text(max_nb_chars=200),
                )
            self.stdout.write("✓ Generated send us messages")

            # Generate Blog Posts
            for _ in range(10):
                BlogPost.objects.create(
                    title=fake.sentence(), body=fake.text(max_nb_chars=2000)
                )
            self.stdout.write("✓ Generated blog posts")

            self.stdout.write(
                self.style.SUCCESS("Successfully generated all fake data!")
            )

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error generating fake data: {str(e)}"))
