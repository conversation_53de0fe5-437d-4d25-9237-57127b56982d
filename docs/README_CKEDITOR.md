# CKEditor Data Entry Guide

This guide will help you understand how to format content in CKEditor fields across our application. These formatting guidelines apply to all CKEditor fields in the following models:

## Models Using CKEditor

1. **BlogPost**
   - Field: `body` - For writing blog content

2. **About**
   - Fields: 
     - `story` - Company/Organization story 
     - `mission` - Mission statement
     - `vision` - Vision statement

3. **Universities**
   - Field: `description` - University details and information

4. **Destination**
   - Field: `description` - Country/Location information

5. **Service**
   - Fields:
     - `offer` - Service offerings
     - `process` - Service process details

6. **TestPreparation**
   - Fields:
     - `modules` - Test preparation modules
     - `process` - Preparation process
     - `features` - Course features

7. **Experience**
   - Field: `description` - Consultancy experience and achievements

## How to Format Content

### Headers
To create a header:
1. Select the text you want to make into a header
2. Click on the "Paragraph" dropdown in the editor toolbar
3. Choose "Heading 3"

Example:
```
### Main Topic Title
```

### Paragraphs
For regular text content:
1. Simply type your text
2. Press Enter for new paragraphs
3. The text will automatically be formatted as a paragraph

Example:
```
This is a paragraph of text that explains the main point. It can be as long as needed to convey the information clearly.
```

### Bullet Points
To create bullet points:
1. Click the bullet point icon in the toolbar (or press `-` and space)
2. Type your point
3. Press Enter for the next point

Example:
```
• First important point
• Second important point
• Third important point
```

### Numbered Lists
To create numbered lists:
1. Click the numbered list icon in the toolbar (or type "1." and space)
2. Type your point
3. Press Enter for the next point (numbers will increment automatically)

Example:
```
1. First step in the process
2. Second step in the process
3. Third step in the process
```

## Examples for Different Models

### 1. BlogPost Body Example
```
### Introduction to Study Abroad

The decision to study abroad is life-changing. This post will guide you through the essential considerations.

### Key Considerations

• Financial planning
• Course selection
• Visa requirements
• Accommodation options

### Conclusion

Making an informed decision about studying abroad requires careful planning and research.
```

### 2. About Model Example (Story Field)
```
### Our Journey

Started in 2010, we have helped thousands of students achieve their dreams of international education.

### Our Growth

• Established first office in City A
• Expanded to 5 locations nationwide
• Partnership with 100+ universities
```

### 3. Service Model Example (Process Field)
```
### Application Process

The application process consists of several key steps that we'll guide you through.

### Step-by-Step Guide

• Initial Consultation
• Document Preparation
• University Selection
• Application Submission
• Visa Assistance

### Timeline

Typically, the entire process takes 3-4 months from start to finish.
```

### 4. TestPreparation Model Example (Modules Field)
```
### Course Structure

Our comprehensive test preparation program is designed for maximum effectiveness.

### Core Modules

1. Reading Comprehension
   - Understanding complex texts
   - Critical analysis skills
   - Speed reading techniques

2. Listening Skills
   - Note-taking strategies
   - Understanding different accents
   - Following complex discussions

3. Writing Tasks
   - Essay structure
   - Academic writing
   - Report writing

4. Speaking Practice
   - Pronunciation drills
   - Interview preparation
   - Presentation skills

### Process

1. Initial Assessment Test
2. Module-wise Training
3. Practice Tests
4. Performance Analysis
5. Targeted Improvement
6. Final Mock Test

### Practice Schedule

Each numbered module includes 20 hours of instruction and 10 hours of practice tests.

### Features

• One-on-one mentoring
• Weekly progress reports
• Practice material access
• Mock test series
```

### 5. Experience Description Example
```
### Our Consultancy Experience

With over a decade of experience in educational consultancy, we have successfully guided thousands of students to achieve their academic goals.

### Key Achievements

• Successfully placed 1000+ students in top universities
• 95% visa success rate
• Partnerships with 50+ international universities
• Expert team of 20+ education consultants

### Our Services Experience

1. Career Counseling
   - Personalized guidance
   - Course selection assistance
   - University shortlisting

2. Application Processing
   - Document preparation
   - Application submission
   - Follow-up management

3. Visa Assistance
   - Documentation support
   - Interview preparation
   - Visa filing expertise

### Success Rate

Our dedicated approach has resulted in:
• 98% student satisfaction
• 90% successful admissions
• 85% scholarship success rate

### Industry Recognition

1. Best Educational Consultancy 2023
2. Top Visa Success Rate Award
3. Excellence in Student Counseling
4. Trusted Partner - Universities Alliance
```

## Tips for Using CKEditor
- Always preview your content before saving
- Use consistent formatting throughout your text
- Keep paragraphs concise and well-organized
- Use bullet points to break down important information
- Make sure headers clearly describe the following content
- Use appropriate spacing between sections
- Keep formatting consistent across similar types of content

## Best Practices by Content Type

### For Blog Posts
- Start with an engaging introduction
- Use headers to break up long content
- Include relevant examples and case studies

### For About Section
- Keep the story concise and engaging
- Use bullet points for key milestones
- Maintain a professional tone

### For Services
- Clearly outline the process steps
- Use bullet points for features and benefits
- Include important details about timing and requirements

### For Test Preparation
- Clearly structure the learning modules
- Include specific time requirements
- List all materials provided

---
Need help with specific formatting or have questions? Please contact the technical team for support. 